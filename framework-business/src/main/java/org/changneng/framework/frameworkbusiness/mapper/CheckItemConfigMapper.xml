<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
        <result column="ITEM_SORT" jdbcType="DECIMAL" property="itemSort" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="LEVEL" jdbcType="DECIMAL" property="level" />
    </resultMap>

    <!-- 查询完整的树形结构数据（使用Oracle递归查询） -->
    <select id="selectTreeStructure" resultMap="BaseResultMap">
        SELECT
            ID,
            ITEM_NAME,
            PARENT_ID,
            ITEM_SORT,
            CREATE_TIME,
            REMARK,
            LEVEL
        FROM CHECK_ITEM_CONFIG
        START WITH PARENT_ID IS NULL OR PARENT_ID = ''
        CONNECT BY PRIOR ID = PARENT_ID
        ORDER SIBLINGS BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 根据父级ID查询树形结构数据 -->
    <select id="selectTreeStructureByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            ID,
            ITEM_NAME,
            PARENT_ID,
            ITEM_SORT,
            CREATE_TIME,
            REMARK,
            LEVEL
        FROM CHECK_ITEM_CONFIG
        <choose>
            <when test="parentId != null and parentId != ''">
                START WITH ID = #{parentId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                START WITH PARENT_ID IS NULL OR PARENT_ID = ''
            </otherwise>
        </choose>
        CONNECT BY PRIOR ID = PARENT_ID
        ORDER SIBLINGS BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>

</mapper>
