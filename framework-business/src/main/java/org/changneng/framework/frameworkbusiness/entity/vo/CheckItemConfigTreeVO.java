package org.changneng.framework.frameworkbusiness.entity.vo;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 环境监管一件事-检查项配置树形结构VO类
 * 简单的两级树形结构（一级父节点 + 二级子节点）
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public class CheckItemConfigTreeVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 检查项标题
     */
    private String itemName;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 排序
     */
    private Integer itemSort;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子节点列表（用于树形结构）
     */
    private List<CheckItemConfigTreeVO> children;
    
    /**
     * 默认构造函数
     */
    public CheckItemConfigTreeVO() {
        super();
        this.children = new ArrayList<>();
        this.hasChildren = false;
        this.expanded = false;
        this.isLeaf = true;
        this.level = 0;
    }
    
    /**
     * 带参构造函数
     * 
     * @param id 主键ID
     * @param itemName 检查项标题
     * @param parentId 父级ID
     * @param itemSort 排序
     * @param createTime 创建时间
     * @param remark 备注
     */
    public CheckItemConfigTreeVO(String id, String itemName, String parentId, Integer itemSort, Date createTime, String remark) {
        this();
        this.id = id;
        this.itemName = itemName;
        this.parentId = parentId;
        this.itemSort = itemSort;
        this.createTime = createTime;
        this.remark = remark;
    }
    
    /**
     * 获取主键ID
     * 
     * @return id 主键ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * 设置主键ID
     * 
     * @param id 主键ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
    
    /**
     * 获取检查项标题
     * 
     * @return itemName 检查项标题
     */
    public String getItemName() {
        return itemName;
    }
    
    /**
     * 设置检查项标题
     * 
     * @param itemName 检查项标题
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }
    
    /**
     * 获取父级ID
     * 
     * @return parentId 父级ID
     */
    public String getParentId() {
        return parentId;
    }
    
    /**
     * 设置父级ID
     * 
     * @param parentId 父级ID
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }
    
    /**
     * 获取排序
     * 
     * @return itemSort 排序
     */
    public Integer getItemSort() {
        return itemSort;
    }
    
    /**
     * 设置排序
     * 
     * @param itemSort 排序
     */
    public void setItemSort(Integer itemSort) {
        this.itemSort = itemSort;
    }
    
    /**
     * 获取创建时间
     * 
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    
    /**
     * 设置创建时间
     * 
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 获取备注
     * 
     * @return remark 备注
     */
    public String getRemark() {
        return remark;
    }
    
    /**
     * 设置备注
     * 
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
    
    /**
     * 获取子节点列表
     * 
     * @return children 子节点列表
     */
    public List<CheckItemConfigTreeVO> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }
    
    /**
     * 设置子节点列表
     * 
     * @param children 子节点列表
     */
    public void setChildren(List<CheckItemConfigTreeVO> children) {
        this.children = children;
        // 自动更新hasChildren和isLeaf属性
        this.hasChildren = children != null && !children.isEmpty();
        this.isLeaf = !this.hasChildren;
    }
    
    /**
     * 添加子节点
     * 
     * @param child 子节点
     */
    public void addChild(CheckItemConfigTreeVO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
        // 自动更新hasChildren和isLeaf属性
        this.hasChildren = true;
        this.isLeaf = false;
    }
    
    /**
     * 获取层级深度
     * 
     * @return level 层级深度
     */
    public Integer getLevel() {
        return level;
    }
    
    /**
     * 设置层级深度
     * 
     * @param level 层级深度
     */
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    /**
     * 获取是否有子节点
     * 
     * @return hasChildren 是否有子节点
     */
    public Boolean getHasChildren() {
        return hasChildren;
    }
    
    /**
     * 设置是否有子节点
     * 
     * @param hasChildren 是否有子节点
     */
    public void setHasChildren(Boolean hasChildren) {
        this.hasChildren = hasChildren;
    }
    
    /**
     * 获取是否展开
     * 
     * @return expanded 是否展开
     */
    public Boolean getExpanded() {
        return expanded;
    }
    
    /**
     * 设置是否展开
     * 
     * @param expanded 是否展开
     */
    public void setExpanded(Boolean expanded) {
        this.expanded = expanded;
    }
    
    /**
     * 获取节点路径
     * 
     * @return nodePath 节点路径
     */
    public String getNodePath() {
        return nodePath;
    }
    
    /**
     * 设置节点路径
     * 
     * @param nodePath 节点路径
     */
    public void setNodePath(String nodePath) {
        this.nodePath = nodePath;
    }
    
    /**
     * 获取是否为叶子节点
     * 
     * @return isLeaf 是否为叶子节点
     */
    public Boolean getIsLeaf() {
        return isLeaf;
    }
    
    /**
     * 设置是否为叶子节点
     * 
     * @param isLeaf 是否为叶子节点
     */
    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }
    
    @Override
    public String toString() {
        return "CheckItemConfigTreeVO{" +
                "id='" + id + '\'' +
                ", itemName='" + itemName + '\'' +
                ", parentId='" + parentId + '\'' +
                ", itemSort=" + itemSort +
                ", createTime=" + createTime +
                ", remark='" + remark + '\'' +
                ", level=" + level +
                ", hasChildren=" + hasChildren +
                ", expanded=" + expanded +
                ", nodePath='" + nodePath + '\'' +
                ", isLeaf=" + isLeaf +
                ", childrenCount=" + (children != null ? children.size() : 0) +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        CheckItemConfigTreeVO that = (CheckItemConfigTreeVO) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
