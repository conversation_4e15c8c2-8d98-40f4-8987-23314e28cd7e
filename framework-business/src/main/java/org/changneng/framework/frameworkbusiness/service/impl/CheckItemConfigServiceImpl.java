package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;

/**
 * 环境监管一件事-检查项配置 Service实现类
 * 简单的两级树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
@Service
@Transactional
public class CheckItemConfigServiceImpl implements CheckItemConfigService {

    private static final Logger logger = LogManager.getLogger(CheckItemConfigServiceImpl.class);

    @Autowired
    private CheckItemConfigMapper checkItemConfigMapper;
    
    /**
     * 获取完整的树形结构数据
     * 核心的树形结构查询方法，将平铺的数据转换为嵌套的树形结构
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructure() {
        try {
            // 查询所有平铺数据
            List<CheckItemConfig> allItems = checkItemConfigMapper.selectAll();

            if (allItems == null || allItems.isEmpty()) {
                return new ArrayList<>();
            }

            // 在Java代码中构建树形结构
            return buildTreeStructureFromFlatData(allItems);

        } catch (Exception e) {
            logger.error("获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据父级ID获取树形结构数据
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructureByParentId(String parentId) {
        try {
            if (ChangnengUtil.isNull(parentId)) {
                // 如果父级ID为空，返回顶级节点的树形结构
                return getTreeStructure();
            }

            // 查询所有数据，然后在Java中过滤和构建子树
            List<CheckItemConfig> allItems = checkItemConfigMapper.selectAll();

            if (allItems == null || allItems.isEmpty()) {
                return new ArrayList<>();
            }

            // 找到指定父级节点
            CheckItemConfig parentNode = null;
            for (CheckItemConfig item : allItems) {
                if (parentId.equals(item.getId())) {
                    parentNode = item;
                    break;
                }
            }

            if (parentNode == null) {
                return new ArrayList<>();
            }

            // 构建以指定节点为根的子树
            return buildSubTreeFromFlatData(allItems, parentId);

        } catch (Exception e) {
            logger.error("根据父级ID获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 验证树形结构的完整性
     * 检查是否存在循环引用、孤立节点等问题
     */
    @Override
    public ResponseJson validateTreeStructure(List<CheckItemConfigTreeVO> treeList) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (treeList == null || treeList.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            }
            
            Set<String> visitedIds = new HashSet<>();
            List<String> issues = new ArrayList<>();
            
            // 递归验证每个节点
            for (CheckItemConfigTreeVO node : treeList) {
                validateNodeRecursively(node, visitedIds, new HashSet<>(), issues);
            }
            
            if (issues.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            } else {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "树形结构验证失败", "树形结构验证失败", issues);
            }
            
        } catch (Exception e) {
            logger.error("验证树形结构失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.QUERY_FAIL.toString(), "验证树形结构失败：" + e.getMessage(), "验证树形结构失败");
        }
    }
    
    /**
     * 从平铺数据构建完整的树形结构
     * 通过Java代码递归构建父子关系
     */
    private List<CheckItemConfigTreeVO> buildTreeStructureFromFlatData(List<CheckItemConfig> flatData) {
        if (flatData == null || flatData.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建ID到对象的映射，便于快速查找
        Map<String, CheckItemConfig> itemMap = new HashMap<>();
        for (CheckItemConfig item : flatData) {
            itemMap.put(item.getId(), item);
        }

        // 找出所有顶级节点（父级ID为空或null）
        List<CheckItemConfig> rootItems = new ArrayList<>();
        for (CheckItemConfig item : flatData) {
            if (ChangnengUtil.isNull(item.getParentId())) {
                rootItems.add(item);
            }
        }

        // 构建树形结构
        List<CheckItemConfigTreeVO> result = new ArrayList<>();
        for (CheckItemConfig rootItem : rootItems) {
            CheckItemConfigTreeVO rootTreeVO = buildTreeNodeRecursively(rootItem, itemMap, 0, rootItem.getItemName());
            result.add(rootTreeVO);
        }

        return result;
    }

    /**
     * 从平铺数据构建子树结构
     * 以指定节点为根构建子树
     */
    private List<CheckItemConfigTreeVO> buildSubTreeFromFlatData(List<CheckItemConfig> flatData, String rootId) {
        if (flatData == null || flatData.isEmpty() || ChangnengUtil.isNull(rootId)) {
            return new ArrayList<>();
        }

        // 创建ID到对象的映射
        Map<String, CheckItemConfig> itemMap = new HashMap<>();
        for (CheckItemConfig item : flatData) {
            itemMap.put(item.getId(), item);
        }

        // 找到根节点
        CheckItemConfig rootItem = itemMap.get(rootId);
        if (rootItem == null) {
            return new ArrayList<>();
        }

        // 构建以指定节点为根的树形结构
        CheckItemConfigTreeVO rootTreeVO = buildTreeNodeRecursively(rootItem, itemMap, 0, rootItem.getItemName());

        List<CheckItemConfigTreeVO> result = new ArrayList<>();
        result.add(rootTreeVO);
        return result;
    }

    /**
     * 递归构建树节点
     *
     * @param currentItem 当前节点的实体对象
     * @param itemMap 所有节点的映射表
     * @param level 当前层级
     * @param nodePath 节点路径
     * @return 构建好的树节点VO
     */
    private CheckItemConfigTreeVO buildTreeNodeRecursively(CheckItemConfig currentItem,
                                                          Map<String, CheckItemConfig> itemMap,
                                                          int level,
                                                          String nodePath) {
        // 转换为TreeVO
        CheckItemConfigTreeVO treeVO = convertToTreeVO(currentItem);
        treeVO.setLevel(level);
        treeVO.setNodePath(nodePath);

        // 查找所有直接子节点
        List<CheckItemConfig> children = new ArrayList<>();
        for (CheckItemConfig item : itemMap.values()) {
            if (currentItem.getId().equals(item.getParentId())) {
                children.add(item);
            }
        }

        // 按排序字段排序子节点
        children.sort((a, b) -> {
            Integer sortA = a.getItemSort();
            Integer sortB = b.getItemSort();

            if (sortA == null && sortB == null) {
                return 0;
            } else if (sortA == null) {
                return 1;
            } else if (sortB == null) {
                return -1;
            } else {
                int result = sortA.compareTo(sortB);
                if (result == 0) {
                    // 如果排序值相同，则按创建时间排序
                    Date timeA = a.getCreateTime();
                    Date timeB = b.getCreateTime();
                    if (timeA != null && timeB != null) {
                        return timeA.compareTo(timeB);
                    }
                }
                return result;
            }
        });

        // 递归构建子节点
        for (CheckItemConfig child : children) {
            String childPath = nodePath + "/" + child.getItemName();
            CheckItemConfigTreeVO childTreeVO = buildTreeNodeRecursively(child, itemMap, level + 1, childPath);
            treeVO.addChild(childTreeVO);
        }

        return treeVO;
    }
    
    /**
     * 将CheckItemConfig转换为CheckItemConfigTreeVO
     */
    private CheckItemConfigTreeVO convertToTreeVO(CheckItemConfig entity) {
        if (entity == null) {
            return null;
        }

        CheckItemConfigTreeVO treeVO = new CheckItemConfigTreeVO();
        treeVO.setId(entity.getId());
        treeVO.setItemName(entity.getItemName());
        treeVO.setParentId(entity.getParentId());
        treeVO.setItemSort(entity.getItemSort());
        treeVO.setCreateTime(entity.getCreateTime());
        treeVO.setRemark(entity.getRemark());

        // 设置默认的前端展示属性
        treeVO.setHasChildren(false);
        treeVO.setExpanded(false);
        treeVO.setIsLeaf(true);
        treeVO.setLevel(0); // 默认层级，会在构建树时重新设置

        return treeVO;
    }
    
    /**
     * 递归验证节点，检查循环引用等问题
     */
    private void validateNodeRecursively(CheckItemConfigTreeVO node, Set<String> globalVisited,
                                       Set<String> currentPath, List<String> issues) {
        if (node == null || ChangnengUtil.isNull(node.getId())) {
            issues.add("发现空节点或节点ID为空");
            return;
        }

        String nodeId = node.getId();

        // 检查循环引用
        if (currentPath.contains(nodeId)) {
            issues.add("发现循环引用，节点ID: " + nodeId + ", 路径: " + node.getNodePath());
            return;
        }

        // 检查重复节点
        if (globalVisited.contains(nodeId)) {
            issues.add("发现重复节点，节点ID: " + nodeId + ", 节点名称: " + node.getItemName());
            return;
        }

        globalVisited.add(nodeId);
        currentPath.add(nodeId);

        // 递归验证子节点
        List<CheckItemConfigTreeVO> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            for (CheckItemConfigTreeVO child : children) {
                Set<String> newCurrentPath = new HashSet<>(currentPath);
                validateNodeRecursively(child, globalVisited, newCurrentPath, issues);
            }
        }
    }
}
