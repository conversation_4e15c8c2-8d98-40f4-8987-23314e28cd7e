package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;

/**
 * 环境监管一件事-检查项配置 Service实现类
 * 简单的两级树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
@Service
@Transactional
public class CheckItemConfigServiceImpl implements CheckItemConfigService {

    private static final Logger logger = LogManager.getLogger(CheckItemConfigServiceImpl.class);

    @Autowired
    private CheckItemConfigMapper checkItemConfigMapper;

    /**
     * 获取完整的两级树形结构数据
     * 一级节点的PARENT_ID为"0"，二级节点的PARENT_ID为对应一级节点的ID
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructure() {
        try {
            // 查询所有平铺数据
            List<CheckItemConfig> allItems = checkItemConfigMapper.selectAll();

            if (allItems == null || allItems.isEmpty()) {
                return new ArrayList<>();
            }

            // 构建两级树形结构
            return buildTwoLevelTreeStructure(allItems);

        } catch (Exception e) {
            logger.error("获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建简单的两级树形结构
     * 一级节点的PARENT_ID为"0"，二级节点的PARENT_ID为对应一级节点的ID
     */
    private List<CheckItemConfigTreeVO> buildTwoLevelTreeStructure(List<CheckItemConfig> allItems) {
        // 分离一级节点和二级节点
        List<CheckItemConfig> firstLevelItems = new ArrayList<>();
        List<CheckItemConfig> secondLevelItems = new ArrayList<>();

        for (CheckItemConfig item : allItems) {
            if ("0".equals(item.getParentId())) {
                // 一级节点
                firstLevelItems.add(item);
            } else {
                // 二级节点
                secondLevelItems.add(item);
            }
        }

        // 按排序字段对一级节点排序
        firstLevelItems.sort((a, b) -> {
            Integer sortA = a.getItemSort();
            Integer sortB = b.getItemSort();

            if (sortA == null && sortB == null) {
                return 0;
            } else if (sortA == null) {
                return 1;
            } else if (sortB == null) {
                return -1;
            } else {
                return sortA.compareTo(sortB);
            }
        });

        // 按排序字段对二级节点排序
        secondLevelItems.sort((a, b) -> {
            Integer sortA = a.getItemSort();
            Integer sortB = b.getItemSort();

            if (sortA == null && sortB == null) {
                return 0;
            } else if (sortA == null) {
                return 1;
            } else if (sortB == null) {
                return -1;
            } else {
                return sortA.compareTo(sortB);
            }
        });

        // 构建树形结构
        List<CheckItemConfigTreeVO> result = new ArrayList<>();

        for (CheckItemConfig firstLevelItem : firstLevelItems) {
            // 转换一级节点
            CheckItemConfigTreeVO firstLevelVO = convertToTreeVO(firstLevelItem);

            // 查找该一级节点下的所有二级节点
            List<CheckItemConfigTreeVO> children = new ArrayList<>();
            for (CheckItemConfig secondLevelItem : secondLevelItems) {
                if (firstLevelItem.getId().equals(secondLevelItem.getParentId())) {
                    CheckItemConfigTreeVO secondLevelVO = convertToTreeVO(secondLevelItem);
                    children.add(secondLevelVO);
                }
            }

            // 设置子节点
            firstLevelVO.setChildren(children);
            result.add(firstLevelVO);
        }

        return result;
    }

    /**
     * 将CheckItemConfig转换为CheckItemConfigTreeVO
     */
    private CheckItemConfigTreeVO convertToTreeVO(CheckItemConfig entity) {
        if (entity == null) {
            return null;
        }

        CheckItemConfigTreeVO treeVO = new CheckItemConfigTreeVO();
        treeVO.setId(entity.getId());
        treeVO.setItemName(entity.getItemName());
        treeVO.setParentId(entity.getParentId());
        treeVO.setItemSort(entity.getItemSort());
        treeVO.setCreateTime(entity.getCreateTime());
        treeVO.setRemark(entity.getRemark());

        return treeVO;
    }
}
